# Eino网络信息采集MCP服务器

这是一个基于MCP (Model Context Protocol) 协议的网络信息采集服务器，专注于为大语言模型提供结构化的网络数据。

## 🚀 功能特性

### 核心信息采集工具
- **analyze_domain** - 单域名/IP信息采集，支持DNS、WHOIS、地理位置、TLS基础信息、技术栈检测
- **analyze_batch** - 批量信息采集，支持并发处理多个目标
- **get_help** - 获取工具使用帮助

### 设计理念
- 🎯 **专注数据采集** - 纯净的信息收集，不进行复杂分析和推理
- 📊 **结构化输出** - 为AI模型优化的数据格式
- 💾 **轻量级设计** - 简化的架构，高效的处理
- 🌍 **中文支持** - 完整的中文界面和文档
- 🛡️ **基础防护** - 速率限制和参数验证

## 📦 安装和运行

### 1. 编译服务器
```bash
cd eino/mcp-server
go build -o mcp-server .
```

### 2. 运行服务器
```bash
./mcp-server
```

### 3. 运行测试
```bash
go run test_client.go
```

## 🔧 配置选项

### 服务器配置
```go
type MCPConfig struct {
    ServerName  string        // 服务器名称
    Version     string        // 版本号
    MaxSessions int           // 最大会话数
    SessionTTL  time.Duration // 会话生存时间
    RateLimit   int           // 速率限制（每分钟）
}
```

### 信息采集深度级别
- **basic** - 基础采集（DNS + WHOIS）
- **standard** - 标准采集（包含地理位置和TLS）
- **full** - 完整采集（所有模块）

## 🛠️ MCP工具详解

### 1. analyze_domain
采集单个域名或IP地址的基础信息

**参数：**
```json
{
  "target": "example.com",           // 必需：目标域名或IP
  "analysis_depth": "standard"       // 可选：信息采集深度
}
```

**响应：**
```json
{
  "target": "example.com",
  "data": { /* 结构化的网络信息 */ },
  "success": true,
  "status": "信息采集成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 2. analyze_batch
批量采集多个目标的基础信息

**参数：**
```json
{
  "targets": ["example.com", "google.com"],  // 必需：目标列表（最多10个）
  "analysis_depth": "basic"                  // 可选：信息采集深度
}
```

**响应：**
```json
{
  "targets": ["example.com", "google.com"],
  "data": [ /* 批量采集结果 */ ],
  "total_count": 2,
  "success_count": 2,
  "status": "批量信息采集完成: 2/2 成功"
}
```

### 3. get_help
获取工具使用帮助

**参数：**
```json
{
  "topic": "overview",        // 可选：帮助主题 (overview/tools/examples)
  "tool_name": ""             // 可选：特定工具的帮助
}
```

## 🔗 与大模型集成

### 工具调用示例
大模型可以通过以下方式调用网络信息采集功能：

```json
{
  "method": "tools/call",
  "params": {
    "name": "analyze_domain",
    "arguments": {
      "target": "example.com",
      "analysis_depth": "standard"
    }
  }
}
```

### 数据结构
服务器返回结构化的数据，便于AI模型处理和分析：

- **DNS信息**: A记录、CNAME记录等
- **WHOIS数据**: 注册信息、到期时间等
- **地理位置**: 国家、城市、经纬度
- **TLS信息**: 证书详情、安全评级
- **技术栈**: 服务器类型、框架识别
- **CDN检测**: CDN提供商识别

## 🛡️ 安全特性

### 1. 速率限制
- 每个会话每分钟最多60次请求
- 自动重置计数器
- 优雅的错误响应

### 2. 参数验证
- JSON Schema验证
- 类型检查和范围验证
- 枚举值验证

### 3. 会话管理
- 自动会话清理
- 上下文隔离
- 内存使用控制

## 🔍 故障排除

### 常见问题

1. **信息采集超时**
   - 解决方案：使用 `basic` 采集深度
   - 检查网络连接

2. **批量采集限制**
   - 单次最多10个目标
   - 考虑分批处理

3. **速率限制**
   - 每分钟最多60次请求
   - 实现客户端重试机制

## 🚀 扩展开发

### 添加新工具
1. 在 `tools.go` 中定义工具
2. 在 `handlers.go` 中实现处理器
3. 注册到服务器

## 📄 许可证

Apache License 2.0 - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- 提交GitHub Issue
- 查看文档和示例
- 使用 `get_help` 工具获取帮助

## 🎯 后续工作

简化后的MCP服务器专注于信息采集，为AI模型提供干净的数据。AI模型可以基于这些数据进行：

- 安全威胁分析
- 风险评估
- 合规性检查
- 网络资产管理
- 仿冒检测

这种分工明确的架构使得系统更加高效和可维护。
