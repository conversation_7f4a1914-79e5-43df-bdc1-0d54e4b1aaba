/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package main

import (
	"context"
	"fmt"
)

// MCPTool MCP工具接口
type MCPTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
	Handler     ToolHandler            `json:"-"`
}

// MCPToolInfo MCP工具信息
type MCPToolInfo struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	InputSchema map[string]interface{} `json:"input_schema"`
}

// ToolHandler 工具处理器函数类型
type ToolHandler func(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error)

// registerTools 注册所有工具
func (s *MCPServer) registerTools() {
	// 1. 域名信息采集工具
	s.registerTool(&MCPTool{
		Name:        "analyze_domain",
		Description: "采集单个域名或IP地址的基础信息，包括DNS、WHOIS、地理位置、TLS等数据",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"target": map[string]interface{}{
					"type":        "string",
					"description": "要采集信息的域名或IP地址",
				},
				"analysis_depth": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"basic", "standard", "full"},
					"description": "信息采集深度级别",
					"default":     "standard",
				},
			},
			"required": []string{"target"},
		},
		Handler: s.handleAnalyzeDomain,
	})

	// 2. 批量信息采集工具
	s.registerTool(&MCPTool{
		Name:        "analyze_batch",
		Description: "批量采集多个域名或IP地址的基础信息，支持并发处理",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"targets": map[string]interface{}{
					"type": "array",
					"items": map[string]interface{}{
						"type": "string",
					},
					"description": "要采集信息的域名或IP地址列表",
					"maxItems":    10, // 限制批量大小
				},
				"analysis_depth": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"basic", "standard"},
					"description": "信息采集深度级别",
					"default":     "basic",
				},
			},
			"required": []string{"targets"},
		},
		Handler: s.handleAnalyzeBatch,
	})

	// 3. 帮助和文档工具
	s.registerTool(&MCPTool{
		Name:        "get_help",
		Description: "获取工具使用帮助和文档信息",
		InputSchema: map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"topic": map[string]interface{}{
					"type":        "string",
					"enum":        []string{"overview", "tools", "examples"},
					"description": "帮助主题",
					"default":     "overview",
				},
				"tool_name": map[string]interface{}{
					"type":        "string",
					"description": "特定工具的帮助（可选）",
				},
			},
		},
		Handler: s.handleGetHelp,
	})
}

// registerTool 注册单个工具
func (s *MCPServer) registerTool(tool *MCPTool) {
	s.tools[tool.Name] = tool
}

// Execute 执行工具
func (t *MCPTool) Execute(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	// 参数验证
	if err := t.validateArguments(args); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 执行工具处理器
	return t.Handler(ctx, args, session, server)
}

// validateArguments 验证工具参数
func (t *MCPTool) validateArguments(args map[string]interface{}) error {
	// 这里应该实现JSON Schema验证
	// 为了简化，我们只做基本的必需参数检查

	schema := t.InputSchema
	properties, ok := schema["properties"].(map[string]interface{})
	if !ok {
		return nil
	}

	required, ok := schema["required"].([]string)
	if !ok {
		return nil
	}

	// 检查必需参数
	for _, field := range required {
		if _, exists := args[field]; !exists {
			return fmt.Errorf("缺少必需参数: %s", field)
		}
	}

	// 检查参数类型（简化版本）
	for field, value := range args {
		if propSchema, exists := properties[field]; exists {
			if err := validateFieldType(field, value, propSchema.(map[string]interface{})); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateFieldType 验证字段类型
func validateFieldType(field string, value interface{}, schema map[string]interface{}) error {
	expectedType, ok := schema["type"].(string)
	if !ok {
		return nil
	}

	switch expectedType {
	case "string":
		if _, ok := value.(string); !ok {
			return fmt.Errorf("参数 %s 应为字符串类型", field)
		}
	case "boolean":
		if _, ok := value.(bool); !ok {
			return fmt.Errorf("参数 %s 应为布尔类型", field)
		}
	case "integer":
		switch v := value.(type) {
		case int, int32, int64, float64:
			// 检查范围
			if min, exists := schema["minimum"]; exists {
				if minVal, ok := min.(float64); ok {
					if getNumericValue(v) < minVal {
						return fmt.Errorf("参数 %s 值过小，最小值为 %v", field, minVal)
					}
				}
			}
			if max, exists := schema["maximum"]; exists {
				if maxVal, ok := max.(float64); ok {
					if getNumericValue(v) > maxVal {
						return fmt.Errorf("参数 %s 值过大，最大值为 %v", field, maxVal)
					}
				}
			}
		default:
			return fmt.Errorf("参数 %s 应为数字类型", field)
		}
	case "array":
		if arr, ok := value.([]interface{}); ok {
			// 检查数组长度
			if maxItems, exists := schema["maxItems"]; exists {
				if maxVal, ok := maxItems.(float64); ok {
					if len(arr) > int(maxVal) {
						return fmt.Errorf("参数 %s 数组长度超出限制，最大长度为 %v", field, maxVal)
					}
				}
			}
		} else {
			return fmt.Errorf("参数 %s 应为数组类型", field)
		}
	}

	// 检查枚举值
	if enum, exists := schema["enum"]; exists {
		if enumValues, ok := enum.([]string); ok {
			valueStr, ok := value.(string)
			if !ok {
				return fmt.Errorf("参数 %s 应为字符串类型以匹配枚举值", field)
			}

			found := false
			for _, enumValue := range enumValues {
				if valueStr == enumValue {
					found = true
					break
				}
			}
			if !found {
				return fmt.Errorf("参数 %s 值 '%s' 不在允许的枚举值中: %v", field, valueStr, enumValues)
			}
		}
	}

	return nil
}

// getNumericValue 获取数值
func getNumericValue(value interface{}) float64 {
	switch v := value.(type) {
	case int:
		return float64(v)
	case int32:
		return float64(v)
	case int64:
		return float64(v)
	case float64:
		return v
	case float32:
		return float64(v)
	default:
		return 0
	}
}
