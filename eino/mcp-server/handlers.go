/*
 * Copyright 2024 CloudWeGo Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package main

import (
	"context"
	"fmt"
	"time"

	"github.com/cloudwego/eino/components/network"
)

// handleAnalyzeDomain 处理域名信息采集请求
func (s *MCPServer) handleAnalyzeDomain(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	target := args["target"].(string)
	depth := getStringArg(args, "analysis_depth", "standard")

	// 根据分析深度配置分析器
	analyzer := s.configureAnalyzerByDepth(depth)

	// 执行网络信息采集
	result, err := analyzer.Analyze(ctx, target)
	if err != nil {
		return nil, fmt.Errorf("网络信息采集失败: %w", err)
	}

	// 构建响应 - 纯净的数据结构，便于AI处理
	response := map[string]interface{}{
		"target":           target,
		"data":             result,
		"collection_depth": depth,
		"timestamp":        time.Now(),
		"success":          result.Success,
	}

	// 添加简单的状态描述
	if result.Success {
		response["status"] = "信息采集成功"
	} else {
		response["status"] = "信息采集失败"
		if result.Error != "" {
			response["error"] = result.Error
		}
	}

	return response, nil
}

// handleAnalyzeBatch 处理批量信息采集请求
func (s *MCPServer) handleAnalyzeBatch(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	targetsInterface := args["targets"].([]interface{})
	targets := make([]string, len(targetsInterface))
	for i, t := range targetsInterface {
		targets[i] = t.(string)
	}

	depth := getStringArg(args, "analysis_depth", "basic")

	// 限制批量大小
	if len(targets) > 10 {
		return nil, fmt.Errorf("批量信息采集最多支持10个目标，当前: %d", len(targets))
	}

	// 配置分析器
	analyzer := s.configureAnalyzerByDepth(depth)

	// 执行批量信息采集
	start := time.Now()
	results, err := analyzer.BatchAnalyze(ctx, targets)
	duration := time.Since(start)

	if err != nil {
		return nil, fmt.Errorf("批量信息采集失败: %w", err)
	}

	// 统计结果
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	// 构建响应 - 纯净的数据结构
	response := map[string]interface{}{
		"targets":           targets,
		"data":              results,
		"total_count":       len(targets),
		"success_count":     successCount,
		"failure_count":     len(targets) - successCount,
		"success_rate":      float64(successCount) / float64(len(targets)) * 100,
		"total_duration_ms": duration.Milliseconds(),
		"collection_depth":  depth,
		"timestamp":         time.Now(),
		"status":            fmt.Sprintf("批量信息采集完成: %d/%d 成功", successCount, len(targets)),
	}

	return response, nil
}

// handleGetHelp 处理帮助请求
func (s *MCPServer) handleGetHelp(ctx context.Context, args map[string]interface{}, session *Session, server *MCPServer) (interface{}, error) {
	topic := getStringArg(args, "topic", "overview")
	toolName := getStringArg(args, "tool_name", "")

	help := s.generateHelpContent(topic, toolName)

	return map[string]interface{}{
		"topic":   topic,
		"content": help,
	}, nil
}

// 辅助函数
func getBoolArg(args map[string]interface{}, key string, defaultValue bool) bool {
	if value, exists := args[key]; exists {
		if boolValue, ok := value.(bool); ok {
			return boolValue
		}
	}
	return defaultValue
}

func getStringArg(args map[string]interface{}, key string, defaultValue string) string {
	if value, exists := args[key]; exists {
		if stringValue, ok := value.(string); ok {
			return stringValue
		}
	}
	return defaultValue
}

func getIntArg(args map[string]interface{}, key string, defaultValue int) int {
	if value, exists := args[key]; exists {
		switch v := value.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return defaultValue
}

// configureAnalyzerByDepth 根据分析深度配置分析器
func (s *MCPServer) configureAnalyzerByDepth(depth string) *network.Analyzer {
	switch depth {
	case "basic":
		return network.NewAnalyzer(
			network.WithGeoLocation(false),
			network.WithTLSSecurity(false),
			network.WithFingerprint(false),
			network.WithWHOISLookup(true),
			network.WithCDNDetection(false),
			network.WithTimeout(15*time.Second),
		)
	case "full":
		return network.NewAnalyzer(
			network.WithGeoLocation(true),
			network.WithTLSSecurity(true),
			network.WithFingerprint(true),
			network.WithWHOISLookup(true),
			network.WithCDNDetection(true),
			network.WithTimeout(60*time.Second),
		)
	default: // "standard"
		return s.analyzer
	}
}

// generateHelpContent 生成帮助内容
func (s *MCPServer) generateHelpContent(topic, toolName string) string {
	if toolName != "" {
		if tool, exists := s.tools[toolName]; exists {
			return fmt.Sprintf("工具: %s\n描述: %s\n\n参数说明:\n%v",
				tool.Name, tool.Description, tool.InputSchema)
		}
		return fmt.Sprintf("未找到工具: %s", toolName)
	}

	switch topic {
	case "overview":
		return `Eino网络信息采集MCP服务器

这是一个基于MCP协议的网络信息采集服务，专注于数据收集：
• 域名和IP地址基础信息采集
• DNS、WHOIS、地理位置查询
• TLS基础信息和网站指纹识别
• 批量信息采集和处理
• 为AI模型提供结构化数据

使用 'get_help' 工具获取更多帮助信息。`

	case "tools":
		toolList := "可用工具列表:\n"
		for name, tool := range s.tools {
			toolList += fmt.Sprintf("• %s: %s\n", name, tool.Description)
		}
		return toolList

	case "examples":
		return `使用示例:

1. 采集单个域名信息:
   工具: analyze_domain
   参数: {"target": "example.com", "analysis_depth": "standard"}

2. 批量信息采集:
   工具: analyze_batch
   参数: {"targets": ["example.com", "google.com"], "analysis_depth": "basic"}

3. 获取帮助:
   工具: get_help
   参数: {"topic": "overview"}`

	default:
		return "未知的帮助主题"
	}
}
